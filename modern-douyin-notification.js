// ==UserScript==
// @name         现代化抖音消息弹窗通知工具
// @namespace    http://tampermonkey.net/
// @version      2.0.0
// @description  现代化的抖音消息弹窗通知工具，采用现代UI/UX设计理念，提供流畅的用户体验
// <AUTHOR>
// @match        https://*.douyin.com/*
// @match        https://www.douyin.com/*
// @icon         https://www.google.com/s2/favicons?sz=64&domain=douyin.com
// @grant        GM_notification
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_registerMenuCommand
// @grant        GM_unregisterMenuCommand
// @grant        unsafeWindow
// @grant        window.focus
// @run-at       document-idle
// @noframes
// @updateURL
// @downloadURL
// ==/UserScript==

/**
 * 现代化抖音消息弹窗通知工具 - Tampermonkey版本
 * 采用现代UI/UX设计理念，提供流畅的用户体验
 * 专为油猴脚本环境优化
 */

(function() {
    'use strict';

    // 等待页面完全加载
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initScript);
    } else {
        initScript();
    }

    function initScript() {
        // 避免重复初始化
        if (unsafeWindow.modernDouyinNotificationInitialized) {
            return;
        }
        unsafeWindow.modernDouyinNotificationInitialized = true;

        console.log('🎉 现代化抖音消息弹窗通知工具已启动');

        // 创建通知实例
        new ModernDouyinNotification();
    }

class ModernDouyinNotification {
    constructor() {
        this.observer = null;
        this.config = { childList: true, subtree: true, attributes: true };
        this.isModalVisible = false;
        this.notificationCount = 0;
        this.lastNotificationTime = 0;
        this.init();
    }

    // 初始化样式
    init() {
        this.injectStyles();
        // 延迟启动观察器，确保页面元素已加载
        setTimeout(() => {
            this.setupObserver();
            this.checkAndShowModal();
        }, 1000);
    }

    // 注入现代化CSS样式
    injectStyles() {
        if (document.getElementById('modern-douyin-styles')) return;

        const style = document.createElement('style');
        style.id = 'modern-douyin-styles';
        style.innerHTML = `
            /* 现代化模态框样式 */
            .modern-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0, 0, 0, 0.6);
                backdrop-filter: blur(8px);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                opacity: 0;
                animation: fadeIn 0.3s ease-out forwards;
            }

            .modern-modal-content {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 24px;
                padding: 0;
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
                width: min(90vw, 420px);
                max-height: 90vh;
                overflow: hidden;
                position: relative;
                transform: scale(0.8) translateY(20px);
                animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
            }

            .modal-header {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                padding: 24px 32px 20px;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                position: relative;
            }

            .modal-icon {
                width: 48px;
                height: 48px;
                background: linear-gradient(135deg, #ff6b6b, #ee5a24);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 16px;
                animation: pulse 2s infinite;
            }

            .modal-icon::before {
                content: "💬";
                font-size: 24px;
                filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
            }

            .modal-title {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 20px;
                font-weight: 600;
                color: #2c3e50;
                margin: 0;
                text-align: center;
                letter-spacing: -0.5px;
            }

            .modal-body {
                padding: 24px 32px;
                background: rgba(255, 255, 255, 0.98);
            }

            .message-content {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 16px;
                line-height: 1.6;
                color: #34495e;
                text-align: center;
                margin: 0;
                padding: 16px 20px;
                background: rgba(116, 75, 162, 0.08);
                border-radius: 16px;
                border-left: 4px solid #764ba2;
            }

            .modal-footer {
                padding: 20px 32px 32px;
                background: rgba(255, 255, 255, 0.98);
                display: flex;
                justify-content: center;
                gap: 12px;
            }

            .modern-btn {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                font-size: 14px;
                font-weight: 500;
                padding: 12px 24px;
                border: none;
                border-radius: 12px;
                cursor: pointer;
                transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                overflow: hidden;
            }

            .btn-primary {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            }

            .btn-primary:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            }

            .btn-primary:active {
                transform: translateY(0);
            }

            .btn-secondary {
                background: rgba(116, 75, 162, 0.1);
                color: #764ba2;
                border: 1px solid rgba(116, 75, 162, 0.2);
            }

            .btn-secondary:hover {
                background: rgba(116, 75, 162, 0.15);
                transform: translateY(-1px);
            }

            /* 动画效果 */
            @keyframes fadeIn {
                to { opacity: 1; }
            }

            @keyframes modalSlideIn {
                to {
                    transform: scale(1) translateY(0);
                }
            }

            @keyframes pulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.05); }
            }

            @keyframes slideOut {
                to {
                    transform: scale(0.9) translateY(-20px);
                    opacity: 0;
                }
            }

            .modal-closing .modern-modal-content {
                animation: slideOut 0.2s ease-in forwards;
            }

            .modal-closing {
                animation: fadeOut 0.3s ease-in forwards;
            }

            @keyframes fadeOut {
                to { opacity: 0; }
            }

            /* 响应式设计 */
            @media (max-width: 480px) {
                .modern-modal-content {
                    width: 95vw;
                    margin: 20px;
                }
                
                .modal-header, .modal-body, .modal-footer {
                    padding-left: 20px;
                    padding-right: 20px;
                }
                
                .modal-footer {
                    flex-direction: column;
                }
                
                .modern-btn {
                    width: 100%;
                }
            }

            /* 深色模式支持 */
            @media (prefers-color-scheme: dark) {
                .modern-modal {
                    background: rgba(0, 0, 0, 0.8);
                }
                
                .modal-header, .modal-body, .modal-footer {
                    background: rgba(30, 30, 30, 0.95);
                }
                
                .modal-title {
                    color: #ecf0f1;
                }
                
                .message-content {
                    color: #bdc3c7;
                    background: rgba(116, 75, 162, 0.15);
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 发送桌面通知 - 优化油猴脚本版本
    sendDesktopNotification(message) {
        const now = Date.now();
        // 防止通知过于频繁（5秒内最多一次）
        if (now - this.lastNotificationTime < 5000) {
            return;
        }
        this.lastNotificationTime = now;

        // 优先使用油猴的GM_notification
        if (typeof GM_notification !== 'undefined') {
            try {
                GM_notification({
                    title: '抖音新消息',
                    text: message,
                    image: 'https://www.douyin.com/favicon.ico',
                    timeout: 5000,
                    onclick: () => {
                        // 点击通知时聚焦到抖音页面
                        window.focus();
                    }
                });
                console.log('📱 已通过GM_notification发送通知');
                return;
            } catch (e) {
                console.warn('GM_notification发送失败，尝试使用浏览器通知:', e);
            }
        }

        // 备用：使用浏览器原生通知
        if ("Notification" in window) {
            if (Notification.permission === "granted") {
                const notification = new Notification("抖音新消息", {
                    body: message,
                    icon: "https://www.douyin.com/favicon.ico",
                    badge: "🔔",
                    tag: 'douyin-message', // 防止重复通知
                    requireInteraction: false
                });

                notification.onclick = () => {
                    window.focus();
                    notification.close();
                };

                // 5秒后自动关闭通知
                setTimeout(() => notification.close(), 5000);
            } else if (Notification.permission !== "denied") {
                Notification.requestPermission().then(permission => {
                    if (permission === "granted") {
                        this.sendDesktopNotification(message);
                    }
                });
            }
        }
    }

    // 显示现代化模态框
    showModal(content) {
        if (this.isModalVisible) return;

        this.isModalVisible = true;
        this.notificationCount++;

        // 保存通知统计到油猴存储
        if (typeof GM_setValue !== 'undefined') {
            GM_setValue('notification_count', this.notificationCount);
        }

        this.sendDesktopNotification(`您有新的消息：${content}`);

        const modal = this.createModalElement(content);
        document.body.appendChild(modal);

        // 添加键盘事件监听
        this.addKeyboardListener(modal);

        console.log(`🔔 显示第${this.notificationCount}条通知: ${content}`);
    }

    // 创建模态框元素
    createModalElement(content) {
        const modal = document.createElement('div');
        modal.className = 'modern-modal';
        
        modal.innerHTML = `
            <div class="modern-modal-content">
                <div class="modal-header">
                    <div class="modal-icon"></div>
                    <h2 class="modal-title">您有新的消息！</h2>
                </div>
                <div class="modal-body">
                    <p class="message-content">${this.escapeHtml(content)}</p>
                </div>
                <div class="modal-footer">
                    <button class="modern-btn btn-secondary" data-action="later">稍后查看</button>
                    <button class="modern-btn btn-primary" data-action="close">立即查看</button>
                </div>
            </div>
        `;

        // 添加事件监听器
        this.addEventListeners(modal);
        
        return modal;
    }

    // 添加事件监听器
    addEventListeners(modal) {
        const modalContent = modal.querySelector('.modern-modal-content');
        
        // 阻止点击内容区域关闭模态框
        modalContent.addEventListener('click', (e) => e.stopPropagation());
        
        // 背景点击关闭
        modal.addEventListener('click', () => this.closeModal(modal));
        
        // 按钮点击事件
        modal.querySelectorAll('.modern-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.dataset.action;
                if (action === 'close') {
                    this.closeModal(modal);
                } else if (action === 'later') {
                    this.closeModal(modal, 5000); // 5秒后重新监听
                }
            });
        });
    }

    // 添加键盘事件监听
    addKeyboardListener(modal) {
        const keyHandler = (e) => {
            if (e.key === 'Escape') {
                this.closeModal(modal);
                document.removeEventListener('keydown', keyHandler);
            }
        };
        document.addEventListener('keydown', keyHandler);
    }

    // 关闭模态框
    closeModal(modal, restartDelay = 10000) {
        modal.classList.add('modal-closing');
        
        setTimeout(() => {
            if (modal.parentNode) {
                document.body.removeChild(modal);
            }
            this.isModalVisible = false;
            
            // 停止监听
            if (this.observer) {
                this.observer.disconnect();
            }
            
            // 延迟后重新开始监听
            setTimeout(() => {
                this.setupObserver();
            }, restartDelay);
        }, 300);
    }

    // HTML转义
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 检查并显示模态框
    checkAndShowModal() {
        const spans = document.querySelectorAll('span[x-semi-prop="count"].semi-badge-count');
        spans.forEach(span => {
            const content = span.innerText || span.textContent;
            if (content && content.trim() !== "") {
                this.showModal(content.trim());
            }
        });
    }

    // 设置观察器
    setupObserver() {
        this.observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                if (mutation.type === 'childList' || mutation.type === 'attributes') {
                    this.checkAndShowModal();
                }
            });
        });
        
        this.observer.observe(document.body, this.config);
    }
}

    // 油猴脚本专用功能
    function addTampermonkeyFeatures() {
        // 添加脚本菜单项（如果支持）
        if (typeof GM_registerMenuCommand !== 'undefined') {
            GM_registerMenuCommand('🔔 测试通知', () => {
                const notification = new ModernDouyinNotification();
                notification.showModal('这是一条测试消息');
            });

            GM_registerMenuCommand('⚙️ 重置脚本', () => {
                if (confirm('确定要重置脚本吗？这将清除所有设置。')) {
                    GM_setValue('notification_count', 0);
                    location.reload();
                }
            });
        }

        // 保存通知统计
        const savedCount = GM_getValue('notification_count', 0);
        console.log(`📊 历史通知次数: ${savedCount}`);
    }

    // 添加油猴专用功能
    addTampermonkeyFeatures();

})(); // 结束IIFE
