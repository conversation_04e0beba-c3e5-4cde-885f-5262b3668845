<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现代化抖音消息弹窗演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .demo-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 600px;
            width: 100%;
        }

        .demo-title {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 16px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .demo-subtitle {
            font-size: 16px;
            color: #7f8c8d;
            margin-bottom: 32px;
            line-height: 1.6;
        }

        .comparison-section {
            margin: 32px 0;
            padding: 24px;
            background: rgba(116, 75, 162, 0.05);
            border-radius: 16px;
            border-left: 4px solid #764ba2;
        }

        .comparison-title {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 16px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 24px 0;
        }

        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            text-align: left;
        }

        .feature-icon {
            font-size: 24px;
            margin-bottom: 12px;
        }

        .feature-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .feature-desc {
            font-size: 14px;
            color: #7f8c8d;
            line-height: 1.5;
        }

        .demo-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            margin: 8px;
        }

        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
        }

        .demo-btn:active {
            transform: translateY(0);
        }

        .test-element {
            display: none;
        }

        .instructions {
            background: rgba(52, 152, 219, 0.1);
            border: 1px solid rgba(52, 152, 219, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin: 24px 0;
            text-align: left;
        }

        .instructions h3 {
            color: #3498db;
            margin-bottom: 12px;
            font-size: 16px;
        }

        .instructions ol {
            color: #34495e;
            line-height: 1.6;
            padding-left: 20px;
        }

        .instructions li {
            margin-bottom: 8px;
        }

        @media (max-width: 768px) {
            .demo-container {
                padding: 24px;
            }
            
            .demo-title {
                font-size: 24px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="demo-title">现代化抖音消息弹窗</h1>
        <p class="demo-subtitle">
            全新设计的消息通知系统，采用现代UI/UX设计理念，<br>
            提供更优雅、流畅的用户体验
        </p>

        <div class="comparison-section">
            <h3 class="comparison-title">✨ 设计改进亮点</h3>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🎨</div>
                    <div class="feature-title">现代化视觉设计</div>
                    <div class="feature-desc">渐变背景、毛玻璃效果、圆角设计，符合当代审美标准</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <div class="feature-title">流畅动画效果</div>
                    <div class="feature-desc">CSS3动画、缓动函数，提供丝滑的交互体验</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <div class="feature-title">响应式布局</div>
                    <div class="feature-desc">完美适配各种屏幕尺寸，移动端友好</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🌙</div>
                    <div class="feature-title">深色模式支持</div>
                    <div class="feature-desc">自动适配系统主题，保护用户视力</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⌨️</div>
                    <div class="feature-title">键盘快捷键</div>
                    <div class="feature-desc">支持ESC键关闭，提升操作效率</div>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🔧</div>
                    <div class="feature-title">面向对象架构</div>
                    <div class="feature-desc">模块化设计，易于维护和扩展</div>
                </div>
            </div>
        </div>

        <div class="instructions">
            <h3>🚀 使用说明</h3>
            <ol>
                <li>点击下方按钮测试新的弹窗效果</li>
                <li>在实际项目中，将 <code>modern-douyin-notification.js</code> 替换原有的 <code>1index.js</code></li>
                <li>新版本会自动监听相同的DOM元素变化</li>
                <li>支持桌面通知权限请求和显示</li>
                <li>提供更好的用户交互体验</li>
            </ol>
        </div>

        <button class="demo-btn" onclick="showTestModal()">🎉 体验新版弹窗</button>
        <button class="demo-btn" onclick="showOldStyleModal()">👴 查看旧版样式对比</button>

        <!-- 隐藏的测试元素，用于触发弹窗 -->
        <span class="test-element semi-badge-count" x-semi-prop="count" id="testSpan"></span>
    </div>

    <script src="modern-douyin-notification.js"></script>
    <script>
        // 测试新版弹窗
        function showTestModal() {
            const testSpan = document.getElementById('testSpan');
            testSpan.textContent = '您有3条新消息';
            
            // 触发DOM变化以激活监听器
            testSpan.style.display = 'block';
            setTimeout(() => {
                testSpan.style.display = 'none';
                testSpan.textContent = '';
            }, 100);
        }

        // 显示旧版样式对比（简化版）
        function showOldStyleModal() {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); display: flex; justify-content: center;
                align-items: center; z-index: 9999;
            `;
            
            const content = document.createElement('div');
            content.style.cssText = `
                background: white; padding: 20px; border-radius: 5px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1); width: 800px; height: 300px;
                text-align: center; animation: shake 0.5s infinite, changeColor 4s infinite;
            `;
            
            content.innerHTML = `
                <h2 style="margin: 0 0 20px 0;">您有新的消息！</h2>
                <p>这是旧版本的样式效果</p>
                <button onclick="this.closest('.old-modal').remove()" 
                        style="background: #FE2C55; color: white; border: none; 
                               padding: 10px 20px; border-radius: 5px; cursor: pointer; 
                               margin-top: 100px;">关闭</button>
            `;
            
            modal.className = 'old-modal';
            modal.appendChild(content);
            document.body.appendChild(modal);
            
            // 添加旧版动画样式
            if (!document.getElementById('old-styles')) {
                const oldStyle = document.createElement('style');
                oldStyle.id = 'old-styles';
                oldStyle.innerHTML = `
                    @keyframes shake {
                        0% { transform: translate(0, 0); }
                        10% { transform: translate(-5px, -5px); }
                        20% { transform: translate(5px, 5px); }
                        30% { transform: translate(-5px, 5px); }
                        40% { transform: translate(5px, -5px); }
                        50% { transform: translate(-5px, -5px); }
                        60% { transform: translate(5px, 5px); }
                        70% { transform: translate(-5px, 5px); }
                        80% { transform: translate(5px, -5px); }
                        90% { transform: translate(-5px, -5px); }
                        100% { transform: translate(0, 0); }
                    }
                    @keyframes changeColor {
                        0% { background-color: rgb(240,124,130); }
                        25% { background-color: rgb(227,189,141); }
                        50% { background-color: rgb(99,187,208); }
                        75% { background-color: rgb(140,194,105); }
                        100% { background-color: rgb(240,124,130); }
                    }
                `;
                document.head.appendChild(oldStyle);
            }
            
            modal.onclick = (e) => {
                if (e.target === modal) modal.remove();
            };
        }

        // 页面加载完成后的提示
        window.addEventListener('load', () => {
            console.log('🎉 现代化抖音消息弹窗演示页面已加载完成！');
            console.log('💡 点击按钮体验新版弹窗效果');
        });
    </script>
</body>
</html>
