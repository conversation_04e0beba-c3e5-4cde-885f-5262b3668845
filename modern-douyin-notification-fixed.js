// ==UserScript==
// @name         现代化抖音消息弹窗通知工具
// @namespace    http://tampermonkey.net/
// @version      2.0.1
// @description  现代化的抖音消息弹窗通知工具，采用现代UI/UX设计理念，提供流畅的用户体验
// <AUTHOR>
// @match        https://*.douyin.com/*
// @match        https://www.douyin.com/*
// @icon         https://www.google.com/s2/favicons?sz=64&domain=douyin.com
// @grant        GM_notification
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_registerMenuCommand
// @run-at       document-idle
// @noframes
// ==/UserScript==

/**
 * 现代化抖音消息弹窗通知工具 - Tampermonkey修复版本
 * 解决兼容性问题，确保在所有油猴环境中正常运行
 */

(function() {
    'use strict';
    
    // 检查是否已经初始化
    if (window.modernDouyinNotificationInitialized) {
        return;
    }
    window.modernDouyinNotificationInitialized = true;
    
    // 等待页面完全加载
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initScript);
    } else {
        // 延迟初始化确保页面元素加载完成
        setTimeout(initScript, 1000);
    }
    
    function initScript() {
        console.log('🎉 现代化抖音消息弹窗通知工具已启动');
        
        // 创建通知实例
        new ModernDouyinNotification();
        
        // 添加油猴专用功能
        addTampermonkeyFeatures();
    }

class ModernDouyinNotification {
    constructor() {
        this.observer = null;
        this.config = { childList: true, subtree: true, attributes: true };
        this.isModalVisible = false;
        this.notificationCount = 0;
        this.lastNotificationTime = 0;
        this.init();
    }

    // 初始化
    init() {
        this.injectStyles();
        this.setupObserver();
        this.checkAndShowModal();
    }

    // 注入现代化CSS样式
    injectStyles() {
        if (document.getElementById('modern-douyin-styles')) return;

        const style = document.createElement('style');
        style.id = 'modern-douyin-styles';
        style.innerHTML = `
            /* 现代化模态框样式 */
            .modern-modal {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                background: rgba(0, 0, 0, 0.6) !important;
                backdrop-filter: blur(8px) !important;
                display: flex !important;
                justify-content: center !important;
                align-items: center !important;
                z-index: 999999 !important;
                opacity: 0 !important;
                animation: modernFadeIn 0.3s ease-out forwards !important;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
            }

            .modern-modal-content {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                border-radius: 24px !important;
                padding: 0 !important;
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25) !important;
                width: min(90vw, 420px) !important;
                max-height: 90vh !important;
                overflow: hidden !important;
                position: relative !important;
                transform: scale(0.8) translateY(20px) !important;
                animation: modernSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) forwards !important;
            }

            .modern-modal-header {
                background: rgba(255, 255, 255, 0.95) !important;
                backdrop-filter: blur(10px) !important;
                padding: 24px 32px 20px !important;
                border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
                position: relative !important;
            }

            .modern-modal-icon {
                width: 48px !important;
                height: 48px !important;
                background: linear-gradient(135deg, #ff6b6b, #ee5a24) !important;
                border-radius: 50% !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                margin: 0 auto 16px !important;
                animation: modernPulse 2s infinite !important;
                font-size: 24px !important;
            }

            .modern-modal-title {
                font-size: 20px !important;
                font-weight: 600 !important;
                color: #2c3e50 !important;
                margin: 0 !important;
                text-align: center !important;
                letter-spacing: -0.5px !important;
            }

            .modern-modal-body {
                padding: 24px 32px !important;
                background: rgba(255, 255, 255, 0.98) !important;
            }

            .modern-message-content {
                font-size: 16px !important;
                line-height: 1.6 !important;
                color: #34495e !important;
                text-align: center !important;
                margin: 0 !important;
                padding: 16px 20px !important;
                background: rgba(116, 75, 162, 0.08) !important;
                border-radius: 16px !important;
                border-left: 4px solid #764ba2 !important;
            }

            .modern-modal-footer {
                padding: 20px 32px 32px !important;
                background: rgba(255, 255, 255, 0.98) !important;
                display: flex !important;
                justify-content: center !important;
                gap: 12px !important;
            }

            .modern-btn {
                font-size: 14px !important;
                font-weight: 500 !important;
                padding: 12px 24px !important;
                border: none !important;
                border-radius: 12px !important;
                cursor: pointer !important;
                transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
                position: relative !important;
                overflow: hidden !important;
            }

            .modern-btn-primary {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                color: white !important;
                box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4) !important;
            }

            .modern-btn-primary:hover {
                transform: translateY(-2px) !important;
                box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6) !important;
            }

            .modern-btn-secondary {
                background: rgba(116, 75, 162, 0.1) !important;
                color: #764ba2 !important;
                border: 1px solid rgba(116, 75, 162, 0.2) !important;
            }

            .modern-btn-secondary:hover {
                background: rgba(116, 75, 162, 0.15) !important;
                transform: translateY(-1px) !important;
            }

            /* 动画效果 */
            @keyframes modernFadeIn {
                to { opacity: 1 !important; }
            }

            @keyframes modernSlideIn {
                to {
                    transform: scale(1) translateY(0) !important;
                }
            }

            @keyframes modernPulse {
                0%, 100% { transform: scale(1) !important; }
                50% { transform: scale(1.05) !important; }
            }

            @keyframes modernSlideOut {
                to {
                    transform: scale(0.9) translateY(-20px) !important;
                    opacity: 0 !important;
                }
            }

            .modern-modal-closing .modern-modal-content {
                animation: modernSlideOut 0.2s ease-in forwards !important;
            }

            .modern-modal-closing {
                animation: modernFadeOut 0.3s ease-in forwards !important;
            }

            @keyframes modernFadeOut {
                to { opacity: 0 !important; }
            }

            /* 响应式设计 */
            @media (max-width: 480px) {
                .modern-modal-content {
                    width: 95vw !important;
                    margin: 20px !important;
                }
                
                .modern-modal-header, .modern-modal-body, .modern-modal-footer {
                    padding-left: 20px !important;
                    padding-right: 20px !important;
                }
                
                .modern-modal-footer {
                    flex-direction: column !important;
                }
                
                .modern-btn {
                    width: 100% !important;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 发送桌面通知
    sendDesktopNotification(message) {
        const now = Date.now();
        // 防止通知过于频繁（5秒内最多一次）
        if (now - this.lastNotificationTime < 5000) {
            return;
        }
        this.lastNotificationTime = now;
        
        // 优先使用油猴的GM_notification
        if (typeof GM_notification !== 'undefined') {
            try {
                GM_notification({
                    title: '抖音新消息',
                    text: message,
                    timeout: 5000,
                    onclick: () => {
                        window.focus();
                    }
                });
                console.log('📱 已通过GM_notification发送通知');
                return;
            } catch (e) {
                console.warn('GM_notification发送失败，尝试使用浏览器通知:', e);
            }
        }
        
        // 备用：使用浏览器原生通知
        if ("Notification" in window) {
            if (Notification.permission === "granted") {
                const notification = new Notification("抖音新消息", {
                    body: message,
                    icon: "https://www.douyin.com/favicon.ico",
                    tag: 'douyin-message'
                });
                
                notification.onclick = () => {
                    window.focus();
                    notification.close();
                };
                
                setTimeout(() => notification.close(), 5000);
            } else if (Notification.permission !== "denied") {
                Notification.requestPermission().then(permission => {
                    if (permission === "granted") {
                        this.sendDesktopNotification(message);
                    }
                });
            }
        }
    }

    // 显示现代化模态框
    showModal(content) {
        if (this.isModalVisible) return;
        
        this.isModalVisible = true;
        this.notificationCount++;
        
        // 保存通知统计
        if (typeof GM_setValue !== 'undefined') {
            try {
                GM_setValue('notification_count', this.notificationCount);
            } catch (e) {
                console.warn('保存统计失败:', e);
            }
        }
        
        this.sendDesktopNotification(`您有新的消息：${content}`);

        const modal = this.createModalElement(content);
        document.body.appendChild(modal);

        // 添加键盘事件监听
        this.addKeyboardListener(modal);
        
        console.log(`🔔 显示第${this.notificationCount}条通知: ${content}`);
    }

    // 创建模态框元素
    createModalElement(content) {
        const modal = document.createElement('div');
        modal.className = 'modern-modal';
        
        modal.innerHTML = `
            <div class="modern-modal-content">
                <div class="modern-modal-header">
                    <div class="modern-modal-icon">💬</div>
                    <h2 class="modern-modal-title">您有新的消息！</h2>
                </div>
                <div class="modern-modal-body">
                    <p class="modern-message-content">${this.escapeHtml(content)}</p>
                </div>
                <div class="modern-modal-footer">
                    <button class="modern-btn modern-btn-secondary" data-action="later">稍后查看</button>
                    <button class="modern-btn modern-btn-primary" data-action="close">立即查看</button>
                </div>
            </div>
        `;

        // 添加事件监听器
        this.addEventListeners(modal);
        
        return modal;
    }

    // 添加事件监听器
    addEventListeners(modal) {
        const modalContent = modal.querySelector('.modern-modal-content');
        
        // 阻止点击内容区域关闭模态框
        modalContent.addEventListener('click', (e) => e.stopPropagation());
        
        // 背景点击关闭
        modal.addEventListener('click', () => this.closeModal(modal));
        
        // 按钮点击事件
        modal.querySelectorAll('.modern-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.dataset.action;
                if (action === 'close') {
                    this.closeModal(modal);
                } else if (action === 'later') {
                    this.closeModal(modal, 5000);
                }
            });
        });
    }

    // 添加键盘事件监听
    addKeyboardListener(modal) {
        const keyHandler = (e) => {
            if (e.key === 'Escape') {
                this.closeModal(modal);
                document.removeEventListener('keydown', keyHandler);
            }
        };
        document.addEventListener('keydown', keyHandler);
    }

    // 关闭模态框
    closeModal(modal, restartDelay = 10000) {
        modal.classList.add('modern-modal-closing');
        
        setTimeout(() => {
            if (modal.parentNode) {
                document.body.removeChild(modal);
            }
            this.isModalVisible = false;
            
            // 停止监听
            if (this.observer) {
                this.observer.disconnect();
            }
            
            // 延迟后重新开始监听
            setTimeout(() => {
                this.setupObserver();
            }, restartDelay);
        }, 300);
    }

    // HTML转义
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 检查并显示模态框
    checkAndShowModal() {
        const spans = document.querySelectorAll('span[x-semi-prop="count"].semi-badge-count');
        spans.forEach(span => {
            const content = span.innerText || span.textContent;
            if (content && content.trim() !== "") {
                this.showModal(content.trim());
            }
        });
    }

    // 设置观察器
    setupObserver() {
        this.observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
                if (mutation.type === 'childList' || mutation.type === 'attributes') {
                    this.checkAndShowModal();
                }
            });
        });
        
        this.observer.observe(document.body, this.config);
    }
}

    // 油猴脚本专用功能
    function addTampermonkeyFeatures() {
        // 添加脚本菜单项（如果支持）
        if (typeof GM_registerMenuCommand !== 'undefined') {
            try {
                GM_registerMenuCommand('🔔 测试通知', () => {
                    const notification = new ModernDouyinNotification();
                    notification.showModal('这是一条测试消息');
                });
                
                GM_registerMenuCommand('⚙️ 重置脚本', () => {
                    if (confirm('确定要重置脚本吗？这将清除所有设置。')) {
                        if (typeof GM_setValue !== 'undefined') {
                            GM_setValue('notification_count', 0);
                        }
                        location.reload();
                    }
                });
                console.log('📋 油猴菜单功能已启用');
            } catch (e) {
                console.warn('菜单功能初始化失败:', e);
            }
        }
        
        // 读取通知统计
        try {
            const savedCount = (typeof GM_getValue !== 'undefined') ? GM_getValue('notification_count', 0) : 0;
            console.log(`📊 历史通知次数: ${savedCount}`);
        } catch (e) {
            console.warn('读取统计数据失败:', e);
        }
    }

})(); // 结束IIFE
