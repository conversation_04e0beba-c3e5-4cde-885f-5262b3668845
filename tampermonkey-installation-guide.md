# 🐒 Tampermonkey 油猴脚本安装指南

## 📋 关于油猴脚本版本

现在的 `modern-douyin-notification.js` 已经完全适配为 Tampermonkey 用户脚本，包含了所有必要的元数据和油猴特有功能。

## 🚀 安装步骤

### 1. 安装 Tampermonkey 扩展
- **Chrome**: [Chrome Web Store](https://chrome.google.com/webstore/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo)
- **Firefox**: [Firefox Add-ons](https://addons.mozilla.org/en-US/firefox/addon/tampermonkey/)
- **Edge**: [Microsoft Store](https://microsoftedge.microsoft.com/addons/detail/tampermonkey/iikmkjmpaadaobahmlepeloendndfphd)
- **Safari**: [App Store](https://apps.apple.com/us/app/tampermonkey/id1482490089)

### 2. 安装脚本
1. 点击浏览器工具栏中的 Tampermonkey 图标
2. 选择 "管理面板" (Dashboard)
3. 点击 "+" 号创建新脚本
4. 删除默认内容，复制粘贴 `modern-douyin-notification.js` 的完整内容
5. 按 `Ctrl+S` (或 `Cmd+S`) 保存脚本

### 3. 验证安装
1. 访问 [抖音网站](https://www.douyin.com)
2. 打开浏览器开发者工具 (F12)
3. 在控制台中应该看到: `🎉 现代化抖音消息弹窗通知工具已启动`

## ⚙️ 脚本配置说明

### 元数据配置
```javascript
// @name         现代化抖音消息弹窗通知工具
// @namespace    http://tampermonkey.net/
// @version      2.0.0
// @description  现代化的抖音消息弹窗通知工具
// <AUTHOR>
// @match        https://*.douyin.com/*
// @match        https://www.douyin.com/*
// @icon         https://www.google.com/s2/favicons?sz=64&domain=douyin.com
// @grant        GM_notification          // 油猴通知API
// @grant        GM_setValue             // 数据存储
// @grant        GM_getValue             // 数据读取
// @grant        GM_registerMenuCommand  // 菜单命令
// @grant        unsafeWindow            // 访问页面window对象
// @run-at       document-idle           // 页面加载完成后运行
// @noframes                            // 不在iframe中运行
```

### 权限说明
- **GM_notification**: 发送系统通知
- **GM_setValue/GM_getValue**: 保存通知统计数据
- **GM_registerMenuCommand**: 添加右键菜单功能
- **unsafeWindow**: 防止脚本重复初始化
- **window.focus**: 点击通知时聚焦页面

## 🎯 油猴版本特有功能

### 1. 增强的通知系统
- **优先使用 GM_notification**: 更稳定的系统通知
- **防重复通知**: 5秒内最多发送一次通知
- **点击通知聚焦**: 点击通知自动切换到抖音页面

### 2. 数据持久化
- **通知统计**: 自动记录通知次数
- **设置保存**: 使用油猴存储API保存用户设置

### 3. 脚本菜单功能
右键点击 Tampermonkey 图标 → 选择脚本名称，可以看到:
- **🔔 测试通知**: 手动触发测试弹窗
- **⚙️ 重置脚本**: 清除所有保存的数据

### 4. 智能初始化
- **防重复加载**: 避免页面刷新时重复初始化
- **延迟启动**: 等待页面元素完全加载后再启动监听
- **错误处理**: 完善的异常捕获和降级处理

## 🔧 自定义配置

### 修改匹配网址
如果需要在其他抖音相关网站使用，可以添加更多 `@match` 规则:
```javascript
// @match        https://creator.douyin.com/*
// @match        https://live.douyin.com/*
```

### 调整通知频率
在代码中找到这一行并修改数值 (毫秒):
```javascript
if (now - this.lastNotificationTime < 5000) { // 5秒改为其他值
```

### 修改监听元素
如果抖音更新了页面结构，可以修改选择器:
```javascript
const spans = document.querySelectorAll('span[x-semi-prop="count"].semi-badge-count');
```

## 🐛 故障排除

### 脚本不工作
1. **检查控制台**: 打开F12查看是否有错误信息
2. **确认网址匹配**: 确保当前页面URL符合 `@match` 规则
3. **重新安装**: 删除脚本后重新安装
4. **清除缓存**: 清除浏览器缓存和Cookie

### 通知不显示
1. **检查浏览器权限**: 确保允许网站发送通知
2. **检查油猴权限**: 确保脚本有 `GM_notification` 权限
3. **测试功能**: 使用脚本菜单中的"测试通知"功能

### 弹窗样式异常
1. **检查CSS冲突**: 可能与网站原有样式冲突
2. **更新脚本**: 确保使用最新版本
3. **清除样式缓存**: 刷新页面重新加载样式

## 📱 移动端支持

虽然主要为桌面端设计，但脚本也支持移动端浏览器:
- **Kiwi Browser** (Android): 支持Chrome扩展
- **Firefox Mobile**: 支持部分扩展功能

## 🔄 更新说明

脚本会自动检查更新 (如果配置了 `@updateURL`)。手动更新步骤:
1. 打开 Tampermonkey 管理面板
2. 找到脚本，点击编辑
3. 替换为新版本代码
4. 保存并刷新页面

## 💡 使用技巧

1. **快速测试**: 使用脚本菜单的测试功能验证效果
2. **查看统计**: 在控制台查看通知统计信息
3. **临时禁用**: 在油猴面板中可以快速启用/禁用脚本
4. **备份设置**: 定期导出脚本配置以防丢失

---

**享受现代化的抖音消息通知体验！** 🎉
