function sendDesktopNotification(message) {
    // 检查浏览器是否支持通知
    if ("Notification" in window) {
        // 检查用户是否已授权通知
        if (Notification.permission === "granted") {
            // 已授权，直接显示通知
            new Notification(message);
        } else if (Notification.permission !== "denied") {
            // 用户未授权，向用户请求授权
            Notification.requestPermission().then(permission => {
                // 如果用户授权，则显示通知
                if (permission === "granted") {
                    new Notification(message);
                }
            });
        }
    } else {
        console.log("此浏览器不支持桌面通知");
    }
}


// 创建并显示模态框的函数
function showModal(content) {
    // 检查是否已经存在模态框，如果存在则不创建新的模态框
    if (document.querySelector('.custom-modal')) return;
	sendDesktopNotification("您在抖音有新的消息：" + content);
	// 发送桌面消息
    // 创建模态框的容器
    var modal = document.createElement('div');
    modal.className = 'custom-modal';
    modal.style.position = 'fixed';
    modal.style.top = '0';
    modal.style.left = '0';
    modal.style.width = '100%';
    modal.style.height = '100%';
    modal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    modal.style.display = 'flex';
    modal.style.justifyContent = 'center';
    modal.style.alignItems = 'center';
    modal.style.zIndex = '999';

    // 创建模态框内容
    var modalContent = document.createElement('div');
    modalContent.style.backgroundColor = 'white';
    modalContent.style.padding = '20px';
    modalContent.style.borderRadius = '5px';
    modalContent.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
    modalContent.style.width = '800px';
    modalContent.style.height = '300px';
    modalContent.style.textAlign = 'center';
    modalContent.style.animation = 'shake 0.5s infinite, changeColor 4s infinite';

    // 阻止点击内容区域关闭模态框
    modalContent.addEventListener('click', function(event) {
        event.stopPropagation();
    });

    // 模态框标题
    var modalTitle = document.createElement('h2');
    modalTitle.innerText = '您有新的消息！';
    modalTitle.style.margin = '0 0 20px 0';
    
    // 模态框内容
    var modalText = document.createElement('p');
    modalText.innerText = content;

    // 模态框关闭按钮
    var closeButton = document.createElement('button');
    closeButton.innerText = '关闭';
    closeButton.style.backgroundColor = '#FE2C55';
    closeButton.style.color = 'white';
    closeButton.style.border = 'none';
    closeButton.style.padding = '10px 20px';
    closeButton.style.borderRadius = '5px';
    closeButton.style.cursor = 'pointer';
    closeButton.style.marginTop = '100px';

    // 关闭按钮的点击事件
    closeButton.onclick = function() {
        document.body.removeChild(modal);
        // 停止监听
        observer.disconnect();
        // 3秒后重新开始监听
        setTimeout(function() {
            observer.observe(document.body, config);
        }, 10000);
    };

    // 背景点击事件，用于关闭模态框
    modal.addEventListener('click', function() {
        document.body.removeChild(modal);
        // 停止监听
        observer.disconnect();
        // 3秒后重新开始监听
        setTimeout(function() {
            observer.observe(document.body, config);
        }, 10000);
    });

    // 将内容添加到模态框
    modalContent.appendChild(modalTitle);
    modalContent.appendChild(modalText);
    modalContent.appendChild(closeButton);
    modal.appendChild(modalContent);

    // 将模态框添加到body
    document.body.appendChild(modal);
    
    // 添加抖动效果的CSS
    var style = document.createElement('style');
    style.innerHTML = `
        @keyframes shake {
            0% { transform: translate(0, 0); }
            10% { transform: translate(-5px, -5px); }
            20% { transform: translate(5px, 5px); }
            30% { transform: translate(-5px, 5px); }
            40% { transform: translate(5px, -5px); }
            50% { transform: translate(-5px, -5px); }
            60% { transform: translate(5px, 5px); }
            70% { transform: translate(-5px, 5px); }
            80% { transform: translate(5px, -5px); }
            90% { transform: translate(-5px, -5px); }
            100% { transform: translate(0, 0); }
        }
        
        @keyframes changeColor {
            0% { background-color: rgb(240,124,130); }
            25% { background-color: rgb(227,189,141); }
            50% { background-color: rgb(99,187,208); }
            75% { background-color: rgb(140,194,105); }
            100% { background-color: rgb(240,124,130); }
        }
    `;
    document.head.appendChild(style);
}

// 检查span标签的值并显示模态框的函数
function checkAndShowModal() {
    var spans = document.querySelectorAll('span[x-semi-prop="count"].semi-badge-count');
    spans.forEach(function(span) {
        var content = span.innerText || span.textContent;
        if (content && content.trim() !== "") {
            showModal(content.trim());
        }
    });
}

// 监听页面内容变化的函数
var observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.type === 'childList' || mutation.type === 'attributes') {
            checkAndShowModal();
        }
    });
});

// 配置MutationObserver
var config = { childList: true, subtree: true, attributes: true };

// 传入目标节点和观察选项
observer.observe(document.body, config);

// 初次加载时检查一次
checkAndShowModal();
